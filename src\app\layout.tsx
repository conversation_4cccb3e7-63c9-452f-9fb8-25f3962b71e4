
import { Gei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";


const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});


export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="theme-color" content="#f97316" />
        <meta name="format-detection" content="telephone=no" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
        suppressHydrationWarning
      >
        {/* ✅ الشريط البرتقالي المتحرك */}
        <div className="bg-blue-600 overflow-hidden whitespace-nowrap">
          <div className="animate-marquee text-white text-sm py-2 px-4 inline-block">
          Ready to your amazing shopping? Get 20% off your first purchase
          </div>
        </div>


            <main className="flex-grow">{children}</main>

      </body>
    </html>
  );
}
